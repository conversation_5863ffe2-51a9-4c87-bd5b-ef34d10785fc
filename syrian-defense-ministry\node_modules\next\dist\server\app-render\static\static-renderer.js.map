{"version": 3, "sources": ["../../../../src/server/app-render/static/static-renderer.ts"], "names": ["DYNAMIC_DATA", "DYNAMIC_HTML", "<PERSON><PERSON><PERSON><PERSON>", "Void<PERSON><PERSON><PERSON>", "createStatic<PERSON><PERSON><PERSON>", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "prerender", "process", "env", "__NEXT_EXPERIMENTAL_REACT", "require", "render", "children", "prelude", "postponed", "stream", "StaticResumeRenderer", "resume", "resumed", "renderToReadableStream", "_children", "ReadableStream", "start", "controller", "close", "data", "ppr", "isStaticGeneration", "streamOptions", "signal", "onError", "onPostpone", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonce", "bootstrapScripts", "formState", "reactPostponedState"], "mappings": ";;;;;;;;;;;;;;;;;;;;IA4FaA,YAAY;eAAZA;;IACAC,YAAY;eAAZA;;IA9CAC,cAAc;eAAdA;;IAYAC,YAAY;eAAZA;;IAgFGC,oBAAoB;eAApBA;;IAhCAC,4BAA4B;eAA5BA;;IANAC,4BAA4B;eAA5BA;;;AArFhB,MAAMC;IAMJC,YAAY,AAAiBC,OAAyB,CAAE;aAA3BA,UAAAA;aAJZC,YAAaC,QAAQC,GAAG,CAACC,yBAAyB,GAC/DC,QAAQ,yBAAyBJ,SAAS,GAC1C;IAEqD;IAEzD,MAAaK,OAAOC,QAAqB,EAAE;QACzC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAG,MAAM,IAAI,CAACR,SAAS,CAACM,UAAU,IAAI,CAACP,OAAO;QAE1E,OAAO;YAAEU,QAAQF;YAASC;QAAU;IACtC;AACF;AAEA,MAAME;IAIJZ,YACE,AAAiBU,SAAiB,EAClC,AAAiBT,OAAsB,CACvC;aAFiBS,YAAAA;aACAT,UAAAA;aALFY,SAASP,QAAQ,yBAC/BO,MAAM;IAKN;IAEH,MAAaN,OAAOC,QAAqB,EAAE;QACzC,MAAMG,SAAS,MAAM,IAAI,CAACE,MAAM,CAACL,UAAU,IAAI,CAACE,SAAS,EAAE,IAAI,CAACT,OAAO;QAEvE,OAAO;YAAEU;YAAQG,SAAS;QAAK;IACjC;AACF;AAEO,MAAMpB;IAIXM,YAAY,AAAiBC,OAAsC,CAAE;aAAxCA,UAAAA;aAHZc,yBAAyBT,QAAQ,yBAC/CS,sBAAsB;IAE6C;IAEtE,MAAaR,OAAOC,QAAqB,EAAyB;QAChE,MAAMG,SAAS,MAAM,IAAI,CAACI,sBAAsB,CAACP,UAAU,IAAI,CAACP,OAAO;QACvE,OAAO;YAAEU;QAAO;IAClB;AACF;AAEO,MAAMhB;IACX,MAAaY,OAAOS,SAAsB,EAAyB;QACjE,OAAO;YACLL,QAAQ,IAAIM,eAAe;gBACzBC,OAAMC,UAAU;oBACd,+BAA+B;oBAC/BA,WAAWC,KAAK;gBAClB;YACF;YACAN,SAAS;QACX;IACF;AACF;AAqBO,MAAMtB,eAAe;AACrB,MAAMC,eAAe;AAQrB,SAASK,6BACduB,IAAY;IAEZ,OAAO;QAAC5B;QAAc4B;KAAK;AAC7B;AAEO,SAASxB;IACd,OAAOL;AACT;AA8BO,SAASI,qBAAqB,EACnC0B,GAAG,EACHC,kBAAkB,EAClBb,SAAS,EACTc,eAAe,EACbC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,gBAAgB,EAChBC,SAAS,EACV,EACO;IACR,IAAIV,KAAK;QACP,IAAIC,oBAAoB;YACtB,sBAAsB;YACtB,OAAO,IAAIxB,eAAe;gBACxB0B;gBACAC;gBACAC;gBACA,oEAAoE;gBACpE,wDAAwD;gBACxDC;gBACAC;gBACAE;YACF;QACF,OAAO;YACL,mBAAmB;YACnB,IAAIrB,cAAclB,cAAc;gBAC9B,mEAAmE;gBACnE,OAAO,IAAIG;YACb,OAAO,IAAIe,WAAW;gBACpB,MAAMuB,sBAAsBvB,SAAS,CAAC,EAAE;gBACxC,sDAAsD;gBACtD,OAAO,IAAIE,qBAAqBqB,qBAAqB;oBACnDR;oBACAC;oBACAC;oBACAG;gBACF;YACF;QACF;IACF;IAEA,IAAIP,oBAAoB;QACtB,wCAAwC;QACxC,OAAO,IAAI7B,eAAe;YACxB+B;YACAC;YACA,0EAA0E;YAC1E,gFAAgF;YAChF,8EAA8E;YAC9E,uBAAuB;YACvBI;YACAC;YACAC;QACF;IACF;IAEA,yCAAyC;IACzC,OAAO,IAAItC,eAAe;QACxB+B;QACAC;QACA,sEAAsE;QACtE,0EAA0E;QAC1EE;QACAC;QACAC;QACAC;QACAC;IACF;AACF"}